<template>
  <a-config-provider
    :locale="zhCN"
    :transformCellText="({ text, column, record, index }) => text || '-'"
    :form="formConfig"
  >
    <a-layout style="min-height: 100vh">
      <router-view />
      <div
        class="w-2 h-2 fixed right-0 bottom-0 bg-ff z-1000"
        v-if="isDD"
      ></div>
    </a-layout>
    <a-modal
      v-model:open="dialogVisible"
      title="提示："
      @cancel="handleOk"
      @ok="handleOk"
      :closable="false"
      :cancelButtonProps="{ style: { display: 'none' } }"
    >
      <p>当前登录用户已更新</p>
      <p>请关闭当前页面重新进入</p>
    </a-modal>
  </a-config-provider>
</template>
<script setup>
import zhCN from "ant-design-vue/es/locale/zh_CN.js";
//ant-design-vue dayjsv3版本 moment是vue2版本

import "dayjs/locale/zh-cn";
import dayjs from "dayjs";
import { env, runtime } from "dingtalk-jsapi";
import {
  reactive,
  toRefs,
  ref,
  computed,
  watch,
  onMounted,
  onBeforeMount,
  getCurrentInstance,
  nextTick,
  onBeforeUnmount,
} from "vue";
import { useUserStore } from "@/stores/user.js";
import { useRoute, useRouter } from "vue-router";
import { logInAppIdOrAgentId } from "@/api/login.js";
import { setToken, getToken, removeToken, removeTenant } from "@/utils/auth.js";
dayjs.locale("zh-cn");
const formConfig = ref({
  colon: false,
});
const isInDingtalk = computed(() => {
  return env.platform != "notInDingTalk";
});
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const isDingTalk = async () => {
  const token = getToken();
  if (token) {
    let res = await logInAppIdOrAgentId();
    if (res.data) {
      await userStore.LogOut();
      nextTick(() => {
        router.push("/login");
      });
    }
  }
};
const isDD = ref(false);
// 接收端（App.vue）
const channel = new BroadcastChannel("auth-channel");
const dialogVisible = ref(false);
// WebChat 资源与可见性控制
const WEBCHAT_CSS_HREF =
  "https://g.alicdn.com/aliyun-documentation/web-chatbot-ui/0.0.24/index.css";
const WEBCHAT_JS_SRC =
  "https://g.alicdn.com/aliyun-documentation/web-chatbot-ui/0.0.24/index.js";
let webchatCssEl = null;
let webchatJsEl = null;

const ensureWebchatAssetsLoaded = () => {
  return new Promise((resolve, reject) => {
    // 注入 CSS（若未注入）
    if (!document.getElementById("webchat-css")) {
      webchatCssEl = document.createElement("link");
      webchatCssEl.id = "webchat-css";
      webchatCssEl.rel = "stylesheet";
      webchatCssEl.crossOrigin = "anonymous";
      webchatCssEl.href = WEBCHAT_CSS_HREF;
      document.head.appendChild(webchatCssEl);
    }

    // 注入 JS（若未注入）
    if (!document.getElementById("webchat-js")) {
      webchatJsEl = document.createElement("script");
      webchatJsEl.id = "webchat-js";
      webchatJsEl.type = "module";
      webchatJsEl.crossOrigin = "anonymous";
      webchatJsEl.src = WEBCHAT_JS_SRC;
      webchatJsEl.onload = () => resolve();
      webchatJsEl.onerror = (e) => reject(e);
      document.body.appendChild(webchatJsEl);
    } else {
      resolve();
    }
  });
};

// 仅依赖 WebChat 自身开关逻辑，不强制覆盖显示/隐藏，避免与内部状态冲突

const destroyWebchatUI = () => {
  try {
    const nodes = document.querySelectorAll(
      ".webchat-container, .webchat-bubble-tip"
    );
    nodes.forEach((node) => {
      if (node && node.parentNode) {
        node.parentNode.removeChild(node);
      }
    });
  } catch (e) {
    // 忽略清理异常以避免阻断导航
  }
};

const reloadWebchatScript = () => {
  try {
    // 确保样式已注入
    if (!document.getElementById("webchat-css")) {
      const link = document.createElement("link");
      link.id = "webchat-css";
      link.rel = "stylesheet";
      link.crossOrigin = "anonymous";
      link.href = WEBCHAT_CSS_HREF;
      document.head.appendChild(link);
    }
  } catch (e) {}

  // 清理可能残留的对话框和气泡入口
  destroyWebchatUI();

  // 移除旧脚本
  try {
    const existing = document.getElementById("webchat-js");
    if (existing && existing.parentNode) {
      existing.parentNode.removeChild(existing);
    }
  } catch (e) {}

  // 注入带时间戳的脚本以破除缓存，确保重新初始化
  return new Promise((resolve, reject) => {
    const fresh = document.createElement("script");
    fresh.id = "webchat-js";
    fresh.type = "module";
    fresh.crossOrigin = "anonymous";
    fresh.src = `${WEBCHAT_JS_SRC}?t=${Date.now()}`;
    fresh.onload = () => resolve();
    fresh.onerror = (e) => reject(e);
    document.body.appendChild(fresh);
  });
};

const initBroadcastChannel = () => {
  channel.value = new BroadcastChannel("auth-channel");
  channel.value.onmessage = async (e) => {
    if (
      e.data.type === "force-refresh" &&
      e.data.tenant_id != userStore.$state.user.tenantId
    ) {
      if (route.path === "/login") {
        // 如果在登录页，不需要处理
        return;
      }
      dialogVisible.value = true;
    }
  };
};
const handleOk = async () => {
  // 刷新页面
  // window.location.reload();
  window.close();
};
watch(
  () => route.path,
  (val) => {
    console.log("[ val ] >", val);
    // if (val == "/login") {
    //   // loadAliChatbot(); // ✅ 登录后且不在登录页，加载助手
    //   const entrance = document.querySelector(".webchat-bubble-tip");
    //   entrance.style.display = "none";
    // } else {
    //   // unloadAliChatbot();
    //   const entrance = document.querySelector(".webchat-bubble-tip");
    //   entrance.style.display = "block";
    // }
    if (val == "/report/saleReport") {
      window.CHATBOT_CONFIG = {
        endpoint:
          "https://webchat-bot-fim-jktrdusadm.cn-hangzhou.fcapp.run/chat",
        displayByDefault: false,
        title: "福瑞助手",
        draggable: true,
        aiChatOptions: {
          conversationOptions: {
            layout: "bubbles",
            conversationStarters: [
              { prompt: "预测一下磷酸铁锂未来半年的价格趋势？" },
              { prompt: "福瑞集团2月份采购了多少种物料？" },
              { prompt: "减速器市场行情?" },
            ],
          },
          displayOptions: {
            height: 600,
          },
          personaOptions: {
            assistant: {
              name: "你好，我是你的 AI 助手",
              avatar:
                "https://img.alicdn.com/imgextra/i2/O1CN01Pda9nq1YDV0mnZ31H_!!6000000003025-54-tps-120-120.apng",
              tagline: "您可以尝试点击下方的快捷入口开启体验！",
            },
          },
          messageOptions: {
            waitTimeBeforeStreamCompletion: "always",
          },
        },
        dataProcessor: {
          rewritePrompt(prompt) {
            return prompt;
          },
        },
      };
      // 若脚本已加载，强制重新加载以触发初始化
      reloadWebchatScript();
    } else {
      window.CHATBOT_CONFIG = undefined;
      destroyWebchatUI();
    }
  },
  {
    immediate: true,
  }
);
onBeforeMount(() => {
  // const entrance = document.querySelector(".webchat-bubble-tip");
  // const container = document.querySelector(".webchat-container");
  // if (entrance && entrance.style) {
  //   entrance.style.display = "none";
  // }
  // if (container && container.style) {
  //   container.style.display = "none";
  // }
});
onMounted(() => {
  const token = getToken();
  if (token) {
    userStore.getInfo();
    if (isInDingtalk.value) {
      initBroadcastChannel();
    }
    // 首屏根据当前路由控制 WebChat
    if (route.path === "/report/saleReport") {
      window.CHATBOT_CONFIG = {
        endpoint:
          "https://webchat-bot-fim-jktrdusadm.cn-hangzhou.fcapp.run/chat",
        displayByDefault: false,
        title: "福瑞助手",
        draggable: true,
        aiChatOptions: {
          conversationOptions: {
            layout: "bubbles",
            conversationStarters: [
              { prompt: "预测一下磷酸铁锂未来半年的价格趋势？" },
              { prompt: "福瑞集团2月份采购了多少种物料？" },
              { prompt: "减速器市场行情?" },
            ],
          },
          displayOptions: {
            height: 600,
          },
          personaOptions: {
            assistant: {
              name: "你好，我是你的 AI 助手",
              avatar:
                "https://img.alicdn.com/imgextra/i2/O1CN01Pda9nq1YDV0mnZ31H_!!6000000003025-54-tps-120-120.apng",
              tagline: "您可以尝试点击下方的快捷入口开启体验！",
            },
          },
          messageOptions: {
            waitTimeBeforeStreamCompletion: "always",
          },
        },
        dataProcessor: {
          rewritePrompt(prompt) {
            return prompt;
          },
        },
      };
      reloadWebchatScript();
    } else {
      window.CHATBOT_CONFIG = undefined;
      destroyWebchatUI();
    }
    // console.log("userStore.$state.user", userStore.$state.roles);
    // if (userStore.$state.roles && userStore.$state.roles[0] == "admin") {
    //   const entrance = document.querySelector(".webchat-bubble-tip");
    //   const container = document.querySelector(".webchat-container");
    //   if (entrance && entrance.style) {
    //     entrance.style.display = "block";
    //   }
    //   if (container && container.style) {
    //     container.style.display = "block";
    //   }
    // } else {
    //   const entrance = document.querySelector(".webchat-bubble-tip");
    //   const container = document.querySelector(".webchat-container");
    //   if (entrance && entrance.style) {
    //     entrance.style.display = "none";
    //   }
    //   if (container && container.style) {
    //     container.style.display = "none";
    //   }
    // }
  } else {
    router.push("/login");
  }
});
onBeforeMount(async () => {
  if (isInDingtalk.value) {
    isDD.value = true;
    await isDingTalk();
  }
});
onBeforeUnmount(() => {
  if (channel.value) {
    channel.value.close();
  }
});
</script>
